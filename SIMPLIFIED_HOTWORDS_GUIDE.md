# 🎯 简化热词使用指南

## 📋 **设计原则**

遵循 **"单一数据源"** 原则，只使用静态热词文件，避免重复维护。

## 🔧 **简化后的架构**

### **唯一的热词配置**
```
📁 app/src/main/assets/hotwords.txt  ← 唯一的热词配置文件
```

### **STT配置**
```kotlin
val config = RecognizerConfig(
    // ... 其他配置
    hotwordsFile = "hotwords.txt",  // 指向assets中的文件
    hotwordsScore = 1.8f,           // 全局权重
)
```

## 📝 **热词文件内容**

### **app/src/main/assets/hotwords.txt**
```
# 运动健身热词文件
# 格式: 词汇 权重(可选)

# 跳绳相关 - 最高优先级
开始跳绳 2.5
结束跳绳 2.5
停止跳绳 2.5
跳绳训练 2.0
跳绳运动 2.0
跳绳锻炼 2.0
跳绳健身 2.0

# AI助手相关 - 高优先级
AI助手 2.0
智能助手 2.0
语音助手 2.0

# 运动指令 - 中等优先级
运动建议 1.8
健身指导 1.8
健康状态 1.8
设备信息 1.5
当前时间 1.5

# 常用动作 - 正常优先级
开始运动 1.3
结束运动 1.3
暂停运动 1.3
继续运动 1.3
打开 1.3
关闭 1.3
启动 1.3
退出 1.3
```

### **✅ 简化设计**
```
优势：
1. 单一数据源，避免重复
2. 配置集中，易于维护
3. 代码简洁，逻辑清晰
4. 修改方便，只需编辑一个文件

代码：
- assets/hotwords.txt (唯一配置)
- RecognizerConfig.hotwordsFile (引用配置)
```

## 🛠️ **使用方式**

### **1. 编辑热词文件**
```
直接编辑: app/src/main/assets/hotwords.txt
添加新词汇: 新词汇 权重
修改权重: 现有词汇 新权重
```

### **2. 权重设置建议**
```
2.5f - 核心功能 (开始跳绳、结束跳绳)
2.0f - 重要指令 (AI助手、跳绳训练)
1.8f - 常用功能 (运动建议、健康状态)
1.5f - 一般词汇 (设备信息、当前时间)
1.3f - 基础动作 (打开、关闭、启动)
```

### **3. 测试验证**
```
1. 修改 hotwords.txt 文件
2. 重新编译应用
3. 测试语音识别准确率
4. 根据效果调整权重
```

## 📊 **维护流程**

### **添加新热词**
```
1. 识别需要提升准确率的词汇
2. 在 hotwords.txt 中添加
3. 设置合适的权重
4. 测试验证效果
```

### **调整权重**
```
1. 观察识别错误的词汇
2. 适当提高权重 (增加0.2-0.5)
3. 测试改进效果
4. 避免权重过高 (>3.0)
```

### **清理无效热词**
```
1. 定期检查热词使用频率
2. 移除不常用的热词
3. 保持文件精简高效
```

## 🎯 **最佳实践**

### **1. 文件组织**
```
✅ 推荐：
- 使用注释分组 (# 跳绳相关)
- 按优先级排序 (高优先级在前)
- 保持格式一致 (词汇 权重)

❌ 避免：
- 混乱的排序
- 缺少注释
- 不一致的格式
```

### **2. 权重设置**
```
✅ 推荐：
- 核心功能使用高权重
- 避免所有词汇都是高权重
- 根据实际效果调整

❌ 避免：
- 权重过高 (>3.0)
- 权重过低 (<0.5)
- 随意设置权重
```

### **3. 测试验证**
```
✅ 推荐：
- 定期测试识别准确率
- 收集用户反馈
- 根据数据调整配置

❌ 避免：
- 设置后不测试
- 忽略用户反馈
- 长期不更新
```

## 🔍 **故障排除**

### **热词不生效**
```
检查项：
1. 文件路径是否正确 (assets/hotwords.txt)
2. 文件格式是否正确 (词汇 权重)
3. 应用是否重新编译
4. 权重是否在合理范围内
```

### **识别准确率没有提升**
```
可能原因：
1. 权重设置过低
2. 词汇选择不当
3. 发音问题
4. 环境噪音干扰

解决方案：
1. 适当提高权重
2. 选择更精确的词汇
3. 改善发音清晰度
4. 减少环境噪音
```

## ✅ **总结**

简化后的热词设计：
- ✅ **单一数据源**: 只有一个热词文件
- ✅ **易于维护**: 修改只需编辑一个文件
- ✅ **逻辑清晰**: 配置和使用分离
- ✅ **避免重复**: 消除了代码和文件的重复

这样的设计更加简洁、可维护，符合软件工程的最佳实践！
