package com.haoxue.libcommon.utils

import androidx.lifecycle.LifecycleCoroutineScope
import com.haoxue.libcommon.network.SafeWebSocketClient
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.launch

/**
 * 安全的 WebSocket 工具类
 * 防止连接失败导致 app 闪退
 */
object SafeWebSocketUtils {

    /**
     * 配置WebSocket重连参数
     */
    fun configureReconnect(
        enabled: Boolean = true,
        maxAttempts: Int = -1,  // -1表示无限重连
        intervalMs: Long = 1000L,
        maxIntervalMs: Long = 30000L
    ) {
        SafeWebSocketClient.configureReconnect(enabled, maxAttempts, intervalMs, maxIntervalMs)
    }

    /**
     * 安全的快速连接（带自动重连）
     * 包含完善的错误处理和自动重连机制，不会导致 app 闪退
     */
    fun safeConnect(
        lifecycleScope: LifecycleCoroutineScope,
        url: String,
        headers: Map<String, String> = emptyMap(),
        onMessage: (String) -> Unit,
        onConnected: () -> Unit = {},
        onDisconnected: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        lifecycleScope.launch {
            try {
                SafeWebSocketClient.connectSafely(
                    url = url,
                    headerList = headers,
                    onMessage = { message ->
                        try {
                            onMessage(message)
                        } catch (e: Exception) {
                            Logcat.e("消息处理回调异常", e)
                            onError("消息处理异常: ${e.message}")
                        }
                    },
                    onConnected = {
                        try {
                            onConnected()
                        } catch (e: Exception) {
                            Logcat.e("连接成功回调异常", e)
                        }
                    },
                    onDisconnected = {
                        try {
                            onDisconnected()
                        } catch (e: Exception) {
                            Logcat.e("断开连接回调异常", e)
                        }
                    },
                    onError = { message, error ->
                        try {
                            onError(message)
                            Logcat.e("WebSocket 连接错误: $message", error)
                        } catch (e: Exception) {
                            Logcat.e("错误回调异常", e)
                        }
                    }
                )
            } catch (e: Exception) {
                try {
                    onError("连接启动异常: ${e.message}")
                    Logcat.e("WebSocket 连接启动异常", e)
                } catch (ex: Exception) {
                    Logcat.e("异常处理回调异常", ex)
                }
            }
        }
    }

    /**
     * 带认证的安全连接
     */
    fun safeConnectWithAuth(
        lifecycleScope: LifecycleCoroutineScope,
        url: String,
        token: String,
        onMessage: (String) -> Unit,
        onConnected: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        val headers = mapOf(
            "Authorization" to "Bearer $token",
            "X-Client-Type" to "Android"
        )

        safeConnect(
            lifecycleScope = lifecycleScope,
            url = url,
            headers = headers,
            onMessage = onMessage,
            onConnected = onConnected,
            onError = onError
        )
    }

    /**
     * 安全发送消息
     */
    fun safeSendMessage(
        lifecycleScope: LifecycleCoroutineScope,
        message: String,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        lifecycleScope.launch {
            try {
                if (!SafeWebSocketClient.isConnected()) {
                    onError("WebSocket 未连接")
                    return@launch
                }

                val result = SafeWebSocketClient.sendTextSafely(message)
                if (result.isSuccess) {
                    onSuccess()
                } else {
                    val error = result.exceptionOrNull()
                    onError("发送失败: ${error?.message}")
                }

            } catch (e: Exception) {
                onError("发送异常: ${e.message}")
                Logcat.e("WebSocket 发送消息异常", e)
            }
        }
    }

    /**
     * 安全断开连接
     */
    fun safeDisconnect(
        lifecycleScope: LifecycleCoroutineScope,
        onDisconnected: () -> Unit = {}
    ) {
        lifecycleScope.launch {
            try {
                SafeWebSocketClient.disconnectSafely()
                onDisconnected()
            } catch (e: Exception) {
                Logcat.e("WebSocket 断开连接失败", e)
                // 即使断开失败也调用回调，因为连接状态已经重置
                onDisconnected()
            }
        }
    }

    /**
     * 手动触发重连
     */
    fun manualReconnect() {
        try {
            SafeWebSocketClient.manualReconnect()
        } catch (e: Exception) {
            Logcat.e("手动重连失败", e)
        }
    }

    /**
     * 获取重连状态信息
     */
    fun getReconnectInfo(): String {
        return try {
            SafeWebSocketClient.getReconnectInfo()
        } catch (e: Exception) {
            "获取重连信息失败: ${e.message}"
        }
    }

    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean {
        return try {
            SafeWebSocketClient.isConnected()
        } catch (e: Exception) {
            Logcat.e("检查连接状态异常", e)
            false
        }
    }

    /**
     * 验证 URL 格式
     */
    fun isValidUrl(url: String): Boolean {
        return try {
            url.isNotBlank() && (url.startsWith("ws://") || url.startsWith("wss://"))
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取错误提示信息
     */
    fun getErrorMessage(error: Throwable?): String {
        return when (error) {
            is java.net.ConnectException -> "连接被拒绝，请检查服务器地址和端口"
            is java.net.SocketTimeoutException -> "连接超时，请检查网络连接"
            is java.net.UnknownHostException -> "无法解析主机名，请检查服务器地址"
            is java.security.cert.CertificateException -> "SSL 证书验证失败"
            else -> error?.message ?: "未知错误"
        }
    }
}
