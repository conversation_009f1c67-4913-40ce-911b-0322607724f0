package com.haoxue.libcommon.audio

import android.content.Context
import android.content.res.AssetFileDescriptor
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.media.SoundPool
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 快速音频播放器
 * 专门针对本地assets文件优化，减少播放延迟
 * 
 * 优化策略：
 * 1. 预加载所有音频文件到SoundPool
 * 2. 使用SoundPool实现近乎同步的播放
 * 3. 支持音频打断和优先级播放
 */
class FastAudioPlayer(private val context: Context) {
    
    private var soundPool: SoundPool? = null
    private val soundMap = ConcurrentHashMap<String, Int>() // 音频路径 -> SoundPool ID
    private val loadingMap = ConcurrentHashMap<String, Boolean>() // 加载状态跟踪
    private var currentStreamId: Int = 0 // 当前播放的stream ID
    
    // 播放状态回调
    private var onPlaybackStateChanged: ((FastAudioState) -> Unit)? = null
    private var onPlaybackError: ((String, Exception?) -> Unit)? = null
    
    init {
        initSoundPool()
    }
    
    /**
     * 初始化SoundPool
     */
    private fun initSoundPool() {
        try {
            val audioAttributes = AudioAttributes.Builder()
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .build()
                
            soundPool = SoundPool.Builder()
                .setMaxStreams(5) // 最多同时播放5个音频
                .setAudioAttributes(audioAttributes)
                .build()
                
            soundPool?.setOnLoadCompleteListener { _, sampleId, status ->
                if (status == 0) {
                    Logcat.d("音频加载完成: sampleId=$sampleId")
                } else {
                    Logcat.e("音频加载失败: sampleId=$sampleId, status=$status")
                }
            }
            
            Logcat.d("FastAudioPlayer SoundPool 初始化完成")
        } catch (e: Exception) {
            Logcat.e("初始化SoundPool失败", e)
            onPlaybackError?.invoke("初始化失败: ${e.message}", e)
        }
    }
    
    /**
     * 预加载音频文件
     * @param audioPath assets中的音频文件路径（如："voice/跳绳播报10.mp3"）
     */
    fun preloadAudio(audioPath: String) {
        try {
            if (soundMap.containsKey(audioPath)) {
                Logcat.d("音频已预加载: $audioPath")
                return
            }
            
            if (loadingMap[audioPath] == true) {
                Logcat.d("音频正在加载中: $audioPath")
                return
            }
            
            loadingMap[audioPath] = true
            
            soundPool?.let { pool ->
                val afd: AssetFileDescriptor = context.assets.openFd(audioPath)
                val soundId = pool.load(afd, 1)
                soundMap[audioPath] = soundId
                
                Logcat.d("开始预加载音频: $audioPath, soundId=$soundId")
                
                // 加载完成后更新状态
                afd.close()
                loadingMap[audioPath] = false
            }
        } catch (e: Exception) {
            Logcat.e("预加载音频失败: $audioPath", e)
            loadingMap[audioPath] = false
            onPlaybackError?.invoke("预加载失败: ${e.message}", e)
        }
    }
    
    /**
     * 批量预加载音频文件
     */
    fun preloadAudios(audioPaths: List<String>) {
        Logcat.d("开始批量预加载 ${audioPaths.size} 个音频文件")
        audioPaths.forEach { path ->
            preloadAudio(path)
        }
    }
    
    /**
     * 快速播放音频（近乎同步）
     * @param audioPath assets中的音频文件路径
     * @param interrupt 是否打断当前播放的音频
     */
    fun playFast(audioPath: String, interrupt: Boolean = false): Boolean {
        try {
            val soundId = soundMap[audioPath]
            if (soundId == null) {
                Logcat.w("音频未预加载，尝试即时加载: $audioPath")
                preloadAudio(audioPath)
                return false
            }
            
            soundPool?.let { pool ->
                // 如果需要打断当前播放
                if (interrupt && currentStreamId != 0) {
                    pool.stop(currentStreamId)
                    Logcat.d("打断当前音频播放")
                }
                
                // 播放音频（几乎无延迟）
                currentStreamId = pool.play(
                    soundId,    // 音频ID
                    1.0f,       // 左声道音量
                    1.0f,       // 右声道音量
                    1,          // 优先级
                    0,          // 循环次数（0=不循环）
                    1.0f        // 播放速率
                )
                
                if (currentStreamId != 0) {
                    onPlaybackStateChanged?.invoke(FastAudioState.PLAYING)
                    Logcat.d("快速播放音频: $audioPath, streamId=$currentStreamId")
                    return true
                } else {
                    Logcat.e("播放音频失败: $audioPath")
                    onPlaybackStateChanged?.invoke(FastAudioState.ERROR)
                    return false
                }
            }
            
            return false
        } catch (e: Exception) {
            Logcat.e("快速播放音频异常: $audioPath", e)
            onPlaybackError?.invoke("播放失败: ${e.message}", e)
            return false
        }
    }
    
    /**
     * 停止当前播放
     */
    fun stop() {
        try {
            if (currentStreamId != 0) {
                soundPool?.stop(currentStreamId)
                currentStreamId = 0
                onPlaybackStateChanged?.invoke(FastAudioState.STOPPED)
                Logcat.d("停止音频播放")
            }
        } catch (e: Exception) {
            Logcat.e("停止播放失败", e)
        }
    }
    
    /**
     * 停止所有播放
     */
    fun stopAll() {
        try {
            soundPool?.autoPause()
            currentStreamId = 0
            onPlaybackStateChanged?.invoke(FastAudioState.STOPPED)
            Logcat.d("停止所有音频播放")
        } catch (e: Exception) {
            Logcat.e("停止所有播放失败", e)
        }
    }
    
    /**
     * 设置播放状态回调
     */
    fun setOnPlaybackStateChanged(callback: (FastAudioState) -> Unit) {
        onPlaybackStateChanged = callback
    }
    
    /**
     * 设置错误回调
     */
    fun setOnPlaybackError(callback: (String, Exception?) -> Unit) {
        onPlaybackError = callback
    }
    
    /**
     * 检查音频是否已预加载
     */
    fun isAudioLoaded(audioPath: String): Boolean {
        return soundMap.containsKey(audioPath)
    }
    
    /**
     * 获取已加载的音频数量
     */
    fun getLoadedAudioCount(): Int {
        return soundMap.size
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            soundPool?.release()
            soundPool = null
            soundMap.clear()
            loadingMap.clear()
            currentStreamId = 0
            Logcat.d("FastAudioPlayer 资源已释放")
        } catch (e: Exception) {
            Logcat.e("释放资源失败", e)
        }
    }
    
    /**
     * 快速音频播放状态
     */
    enum class FastAudioState {
        IDLE,       // 空闲
        PLAYING,    // 播放中
        STOPPED,    // 已停止
        ERROR       // 错误
    }
}
