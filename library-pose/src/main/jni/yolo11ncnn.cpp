// Ten<PERSON> is pleased to support the open source community by making ncnn available.
//
// Copyright (C) 2025 THL A29 Limited, a Tencent company. All rights reserved.
//
// Licensed under the BSD 3-Clause License (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// https://opensource.org/licenses/BSD-3-Clause
//
// Unless required by applicable law or agreed to in writing, software distributed
// under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
// CONDITIONS OF ANY KIND, either express or implied. See the License for the
// specific language governing permissions and limitations under the License.

#include <android/asset_manager_jni.h>
#include <android/native_window_jni.h>
#include <android/native_window.h>

#include <android/log.h>

#include <jni.h>

#include <string>
#include <vector>
#include <stdlib.h>

#include <platform.h>
#include <benchmark.h>

#include "yolo11.h"

#include "ndkcamera.h"

#include <opencv2/core/core.hpp>
#include <opencv2/imgproc/imgproc.hpp>

#if __ARM_NEON
#include <arm_neon.h>
#endif // __ARM_NEON

// 添加全局变量存储回调对象
static jobject g_callback_obj = NULL;
static jmethodID g_callback_mid = NULL;
static JavaVM* g_jvm = NULL;

// 共享关键点缓冲区
static float* g_shared_buf = nullptr;
static size_t g_shared_capacity = 0; // 容纳的 float 数
static jobject g_direct_buf = NULL; // 全局 DirectByteBuffer 引用

static int draw_unsupported(cv::Mat& rgb)
{
    const char text[] = "unsupported";

    int baseLine = 0;
    cv::Size label_size = cv::getTextSize(text, cv::FONT_HERSHEY_SIMPLEX, 1.0, 1, &baseLine);

    int y = (rgb.rows - label_size.height) / 2;
    int x = (rgb.cols - label_size.width) / 2;

    cv::rectangle(rgb, cv::Rect(cv::Point(x, y), cv::Size(label_size.width, label_size.height + baseLine)),
                    cv::Scalar(255, 255, 255), -1);

    cv::putText(rgb, text, cv::Point(x, y + label_size.height),
                cv::FONT_HERSHEY_SIMPLEX, 1.0, cv::Scalar(0, 0, 0));

    return 0;
}

static int draw_fps(cv::Mat& rgb)
{
    // resolve moving average
    float avg_fps = 0.f;
    {
        static double t0 = 0.f;
        static float fps_history[10] = {0.f};

        double t1 = ncnn::get_current_time();
        if (t0 == 0.f)
        {
            t0 = t1;
            return 0;
        }

        float fps = 1000.f / (t1 - t0);
        t0 = t1;

        for (int i = 9; i >= 1; i--)
        {
            fps_history[i] = fps_history[i - 1];
        }
        fps_history[0] = fps;

        if (fps_history[9] == 0.f)
        {
            return 0;
        }

        for (int i = 0; i < 10; i++)
        {
            avg_fps += fps_history[i];
        }
        avg_fps /= 10.f;
    }

    char text[32];
    sprintf(text, "FPS=%.2f", avg_fps);

    int baseLine = 0;
    cv::Size label_size = cv::getTextSize(text, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);

    int y = 0;
    int x = rgb.cols - label_size.width;

    cv::rectangle(rgb, cv::Rect(cv::Point(x, y), cv::Size(label_size.width, label_size.height + baseLine)),
                    cv::Scalar(255, 255, 255), -1);

    cv::putText(rgb, text, cv::Point(x, y + label_size.height),
                cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0));

    return 0;
}

static YOLO11* g_yolo11 = 0;
static ncnn::Mutex lock;


// 调用回调的辅助函数
static void call_pose_callback(const std::vector<Object>& objects, int img_width, int img_height)
{
    if (g_callback_obj == NULL || g_callback_mid == NULL || g_jvm == NULL)
    {
        return;
    }

    // 计算需要的 float 数量
    const int personCount = objects.size();
    const size_t needed = (size_t)personCount * 17 * 3;

    if (needed == 0)
    {
        return;
    }

    // 如果容量不足，需要重新分配（线程安全由上层锁保证）
    if (needed > g_shared_capacity)
    {
        // 简单处理：跳过写入，以免越界
        __android_log_print(ANDROID_LOG_WARN, "yolo11ncnn", "Shared buffer too small: %zu < %zu", g_shared_capacity, needed);
        return;
    }

    // 写入数据到共享缓冲区
    size_t index = 0;
    for (int i = 0; i < personCount; i++)
    {
        const Object& obj = objects[i];
        for (int j = 0; j < 17; j++)
        {
            if (j < (int)obj.keypoints.size())
            {
                g_shared_buf[index++] = obj.keypoints[j].p.x;
                g_shared_buf[index++] = obj.keypoints[j].p.y;
                g_shared_buf[index++] = obj.keypoints[j].prob;
            }
            else
            {
                g_shared_buf[index++] = 0.f;
                g_shared_buf[index++] = 0.f;
                g_shared_buf[index++] = 0.f;
            }
        }
    }

    JNIEnv* env = NULL;
    bool attached = false;

    if (g_jvm->GetEnv((void**)&env, JNI_VERSION_1_4) != JNI_OK)
    {
        if (g_jvm->AttachCurrentThread(&env, NULL) != JNI_OK)
        {
            return;
        }
        attached = true;
    }

    // 回调 Kotlin，无需再传数组
    env->CallVoidMethod(g_callback_obj, g_callback_mid, personCount, img_width, img_height);

    if (attached)
    {
        g_jvm->DetachCurrentThread();
    }
}

class MyNdkCamera : public NdkCameraWindow
{
public:
    virtual void on_image_render(cv::Mat& rgb) const;
};

void MyNdkCamera::on_image_render(cv::Mat& rgb) const
{
    // yolo11
    {
        ncnn::MutexLockGuard g(lock);

        if (g_yolo11)
        {
            std::vector<Object> objects;
            g_yolo11->detect(rgb, objects);

            // 调用回调函数，传递检测结果
            call_pose_callback(objects, rgb.cols, rgb.rows);

            g_yolo11->draw(rgb, objects);
        }
        else
        {
            draw_unsupported(rgb);
        }
    }

    draw_fps(rgb);
}

static MyNdkCamera* g_camera = 0;

extern "C" {

JNIEXPORT jint JNI_OnLoad(JavaVM* vm, void* reserved)
{
    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "JNI_OnLoad");

    g_camera = new MyNdkCamera;
    g_jvm = vm; // 保存JavaVM指针

    ncnn::create_gpu_instance();

    return JNI_VERSION_1_4;
}

JNIEXPORT void JNI_OnUnload(JavaVM* vm, void* reserved)
{
    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "JNI_OnUnload");

    {
        ncnn::MutexLockGuard g(lock);

        delete g_yolo11;
        g_yolo11 = 0;


        // 清理回调对象
        JNIEnv* env = NULL;
        if (g_jvm->GetEnv((void**)&env, JNI_VERSION_1_4) == JNI_OK && env != NULL)
        {
            if (g_callback_obj)
            {
                env->DeleteGlobalRef(g_callback_obj);
                g_callback_obj = NULL;
            }
        }
        g_callback_mid = NULL;
    }

    ncnn::destroy_gpu_instance();

    delete g_camera;
    g_camera = 0;
}

// 注册回调的JNI方法
JNIEXPORT jboolean JNICALL Java_com_tencent_yolo11ncnn_YOLO11Ncnn_registerPoseCallback(JNIEnv* env, jobject thiz, jobject callback)
{
    ncnn::MutexLockGuard g(lock);

    // 清理旧的回调对象
    if (g_callback_obj)
    {
        env->DeleteGlobalRef(g_callback_obj);
        g_callback_obj = NULL;
    }

    if (callback == NULL)
    {
        return JNI_FALSE;
    }

    // 获取回调方法ID
    jclass callback_class = env->GetObjectClass(callback);
    if (callback_class == NULL)
    {
        return JNI_FALSE;
    }

    g_callback_mid = env->GetMethodID(callback_class, "onPoseDetected", "(III)V");
    if (g_callback_mid == NULL)
    {
        return JNI_FALSE;
    }

    // 创建全局引用
    g_callback_obj = env->NewGlobalRef(callback);

    return JNI_TRUE;
}

// 取消注册回调的JNI方法
JNIEXPORT jboolean JNICALL Java_com_tencent_yolo11ncnn_YOLO11Ncnn_unregisterPoseCallback(JNIEnv* env, jobject thiz)
{
    ncnn::MutexLockGuard g(lock);

    if (g_callback_obj)
    {
        env->DeleteGlobalRef(g_callback_obj);
        g_callback_obj = NULL;
    }

    g_callback_mid = NULL;

    return JNI_TRUE;
}

// public native boolean loadModel(AssetManager mgr, int modelid, int cpugpu);
JNIEXPORT jboolean JNICALL Java_com_tencent_yolo11ncnn_YOLO11Ncnn_loadModel(JNIEnv* env, jobject thiz, jobject assetManager, jint modelid, jint cpugpu)
{
    if (modelid < 0 || modelid > 8 || cpugpu < 0 || cpugpu > 2)
    {
        return JNI_FALSE;
    }

    AAssetManager* mgr = AAssetManager_fromJava(env, assetManager);

    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "loadModel %p", mgr);

    const char* modeltypes[9] =
    {
        "n",
        "s",
        "m",
        "n",
        "s",
        "m",
        "n",
        "s",
        "m"
    };

    std::string parampath = std::string("yolo11") + modeltypes[(int)modelid] + "_pose.ncnn.param";
    std::string modelpath = std::string("yolo11") + modeltypes[(int)modelid] + "_pose.ncnn.bin";
    bool use_gpu = (int)cpugpu == 1;
    bool use_turnip = (int)cpugpu == 2;

    {
        ncnn::MutexLockGuard g(lock);

        {
            static int old_modelid = 0;
            static int old_cpugpu = 0;
            if ((modelid % 3) != old_modelid || cpugpu != old_cpugpu)
            {
                delete g_yolo11;
                g_yolo11 = 0;
            }
            old_modelid = modelid % 3;
            old_cpugpu = cpugpu;

            ncnn::destroy_gpu_instance();

            if (use_turnip)
            {
                ncnn::create_gpu_instance("libvulkan_freedreno.so");
            }
            else if (use_gpu)
            {
                ncnn::create_gpu_instance();
            }

            if (!g_yolo11)
            {
                g_yolo11 = new YOLO11_pose;
                g_yolo11->load(mgr, parampath.c_str(), modelpath.c_str(), use_gpu || use_turnip);
            }
            int target_size = 320;
            if ((int)modelid >= 3)
                target_size = 480;
            if ((int)modelid >= 6)
                target_size = 640;
            g_yolo11->set_det_target_size(target_size);
        }
    }

    return JNI_TRUE;
}

// public native boolean openCamera(int facing);
JNIEXPORT jboolean JNICALL Java_com_tencent_yolo11ncnn_YOLO11Ncnn_openCamera(JNIEnv* env, jobject thiz, jint facing)
{
    if (facing < 0 || facing > 1)
        return JNI_FALSE;

    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "openCamera %d", facing);

    g_camera->open((int)facing);

    return JNI_TRUE;
}

// public native boolean closeCamera();
JNIEXPORT jboolean JNICALL Java_com_tencent_yolo11ncnn_YOLO11Ncnn_closeCamera(JNIEnv* env, jobject thiz)
{
    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "closeCamera");

    g_camera->close();

    return JNI_TRUE;
}

// public native boolean setOutputWindow(Surface surface);
JNIEXPORT jboolean JNICALL Java_com_tencent_yolo11ncnn_YOLO11Ncnn_setOutputWindow(JNIEnv* env, jobject thiz, jobject surface)
{
    ANativeWindow* win = ANativeWindow_fromSurface(env, surface);

    __android_log_print(ANDROID_LOG_DEBUG, "ncnn", "setOutputWindow %p", win);

    g_camera->set_window(win);

    return JNI_TRUE;
}

// ------------------ 共享缓冲区初始化 ------------------

extern "C" JNIEXPORT jobject JNICALL
Java_com_tencent_yolo11ncnn_YOLO11Ncnn_initSharedBuffer(JNIEnv* env, jobject thiz, jint maxPerson)
{
    ncnn::MutexLockGuard g(lock);

    const size_t capacity = (size_t)maxPerson * 17 * 3; // float 数

    // 如果已存在且容量足够则复用
    if (g_shared_buf && g_shared_capacity >= capacity && g_direct_buf)
    {
        return g_direct_buf;
    }

    // 释放旧资源
    if (g_shared_buf)
    {
        free(g_shared_buf);
        g_shared_buf = nullptr;
        g_shared_capacity = 0;
    }

    g_shared_buf = (float*)malloc(capacity * sizeof(float));
    if (!g_shared_buf)
    {
        return nullptr;
    }
    g_shared_capacity = capacity;

    // 创建 DirectByteBuffer
    jobject directBuffer = env->NewDirectByteBuffer(g_shared_buf, capacity * sizeof(float));

    // 存成全局引用
    if (g_direct_buf)
    {
        env->DeleteGlobalRef(g_direct_buf);
    }
    g_direct_buf = env->NewGlobalRef(directBuffer);

    return g_direct_buf;
}

}
