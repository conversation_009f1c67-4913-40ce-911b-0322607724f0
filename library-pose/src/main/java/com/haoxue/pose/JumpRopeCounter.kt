package com.haoxue.pose

import com.tencent.yolo11ncnn.MotionCounter

class JumpRopeCounter : MotionCounter {

    private val map = mutableMapOf<Int, Int>()
    private val mapJump = mutableMapOf<Int, CountData>()

    private var maxPerson: Int = 1

    override fun processPerson(
        id: Int,
        imageWidth: Int,
        imageHeight: Int,
        keypoints: FloatArray
    ): Int? {
        val counter = mapJump.getOrPut(id) { CountData() }

        var resultCount: Int? = null
        counter.update(keypoints) { newCount -> resultCount = newCount }
        return resultCount
    }

    override fun reset() {
        mapJump.forEach { it.value.reset() }
        mapJump.clear()
        map.clear()
    }

    override fun setMaxPerson(maxPerson: Int) {
        this.maxPerson = maxPerson
    }
}