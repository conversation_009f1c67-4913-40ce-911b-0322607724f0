package com.tencent.yolo11ncnn

/**
 * 统一的运动计数抽象接口。
 * - 每帧调用 [processFrame]，内部解析关键点并更新计数。
 * - 只有当计数结果变化时才通过 [callback] 通知外部，减少 UI 刷新。
 */
interface MotionCounter {
    /** 配置当前运动一次最多计数的人数；默认 1 */
    fun setMaxPerson(maxPerson: Int) {}

    /**
     * 处理单人关键点。
     * @param id          稳定的 personId
     * @param imageWidth  原始图像宽
     * @param imageHeight 原始图像高
     * @param keypoints   该人的 51 个 float 关键点数组
     * @return 若计数有变化，返回最新计数；否则返回 null
     */
    fun processPerson(
        id: Int,
        imageWidth: Int,
        imageHeight: Int,
        keypoints: FloatArray
    ): Int?

    /** 重置内部状态 */
    fun reset()

    /** 有需要时释放资源 */
    fun release() {}
} 