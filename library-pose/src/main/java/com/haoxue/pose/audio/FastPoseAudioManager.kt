package com.haoxue.pose.audio

import android.content.Context
import com.haoxue.libcommon.audio.FastAudioPlayer
import com.lazy.library.logging.Logcat

/**
 * 快速姿态音频管理器
 * 使用SoundPool实现近乎同步的音频播放，专门解决快速跳绳时的音频延迟问题
 */
class FastPoseAudioManager {
    companion object {
        private const val AUDIO_BASE_PATH = "voice/"
        private const val AUDIO_EXTENSION = ".mp3"
        private lateinit var fastAudioPlayer: FastAudioPlayer
        
        private var lastPlayedAudio = ""
        private var lastPlayTime = 0L
        
        /**
         * 初始化快速音频播放器
         */
        fun initialize(context: Context) {
            fastAudioPlayer = FastAudioPlayer(context)
            
            // 预加载所有计数音频
            preloadAllCountAudios()
            
            Logcat.d("FastPoseAudioManager 初始化完成")
        }
        
        /**
         * 预加载所有计数音频
         */
        private fun preloadAllCountAudios() {
            val audioList = listOf(
                // 系统提示音
                getFullPath(PoseAudio.WELCOME),
                getFullPath(PoseAudio.READY),
                getFullPath(PoseAudio.START),
                getFullPath(PoseAudio.JUMP),
                getFullPath(PoseAudio.BEYOND_SCREEN),
                
                // 计数音频
                getFullPath(PoseAudio.COUNT_10),
                getFullPath(PoseAudio.COUNT_20),
                getFullPath(PoseAudio.COUNT_30),
                getFullPath(PoseAudio.COUNT_40),
                getFullPath(PoseAudio.COUNT_50),
                getFullPath(PoseAudio.COUNT_60),
                getFullPath(PoseAudio.COUNT_70),
                getFullPath(PoseAudio.COUNT_80),
                getFullPath(PoseAudio.COUNT_90),
                getFullPath(PoseAudio.COUNT_100),
                getFullPath(PoseAudio.COUNT_110),
                getFullPath(PoseAudio.COUNT_120),
                getFullPath(PoseAudio.COUNT_150),
                getFullPath(PoseAudio.COUNT_200),
                getFullPath(PoseAudio.COUNT_250),
                getFullPath(PoseAudio.COUNT_300),
                getFullPath(PoseAudio.COUNT_400),
                getFullPath(PoseAudio.COUNT_500),
                getFullPath(PoseAudio.COUNT_600),
                getFullPath(PoseAudio.COUNT_700),
                getFullPath(PoseAudio.COUNT_800)
            )
            
            fastAudioPlayer.preloadAudios(audioList)
            Logcat.d("开始预加载 ${audioList.size} 个音频文件")
        }
        
        /**
         * 播放指定音频（快速播放）
         */
        fun playFast(audio: PoseAudio, interrupt: Boolean = false) {
            val audioPath = getFullPath(audio)
            val currentTime = System.currentTimeMillis()
            
            // 防止重复播放相同音频（100ms内）
            if (audioPath == lastPlayedAudio && currentTime - lastPlayTime < 100) {
                return
            }
            
            val success = fastAudioPlayer.playFast(audioPath, interrupt)
            if (success) {
                lastPlayedAudio = audioPath
                lastPlayTime = currentTime
                Logcat.d("快速播放音频: ${audio.path}")
            } else {
                Logcat.w("快速播放失败: ${audio.path}")
            }
        }
        
        /**
         * 快速播放计数音频
         */
        fun playCountFast(num: Int, interrupt: Boolean = true) {
            val audio = when (num) {
                10 -> PoseAudio.COUNT_10
                20 -> PoseAudio.COUNT_20
                30 -> PoseAudio.COUNT_30
                40 -> PoseAudio.COUNT_40
                50 -> PoseAudio.COUNT_50
                60 -> PoseAudio.COUNT_60
                70 -> PoseAudio.COUNT_70
                80 -> PoseAudio.COUNT_80
                90 -> PoseAudio.COUNT_90
                100 -> PoseAudio.COUNT_100
                110 -> PoseAudio.COUNT_110
                120 -> PoseAudio.COUNT_120
                150 -> PoseAudio.COUNT_150
                200 -> PoseAudio.COUNT_200
                250 -> PoseAudio.COUNT_250
                300 -> PoseAudio.COUNT_300
                400 -> PoseAudio.COUNT_400
                500 -> PoseAudio.COUNT_500
                600 -> PoseAudio.COUNT_600
                700 -> PoseAudio.COUNT_700
                800 -> PoseAudio.COUNT_800
                else -> PoseAudio.JUMP
            }
            
            playFast(audio, interrupt)
        }
        
        /**
         * 智能播放计数音频
         * 根据跳绳速度自动选择播放策略
         */
        fun smartPlayCount(count: Int, speed: Float) {
            // 根据速度决定是否打断当前播放
            val interrupt = when {
                speed <= 1.5f -> false  // 慢速：不打断
                speed <= 3.0f -> true   // 正常：打断
                speed <= 5.0f -> true   // 快速：打断
                else -> true            // 超快：打断
            }
            
            // 根据速度决定是否播放
            val shouldPlay = when {
                speed <= 1.5f -> {
                    // 慢速：播放所有重要计数
                    count in listOf(10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 150, 200, 250, 300, 400, 500, 600, 700, 800)
                }
                speed <= 3.0f -> {
                    // 正常：播放重要节点
                    count % 10 == 0 || count in listOf(50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 800)
                }
                speed <= 5.0f -> {
                    // 快速：只播放里程碑
                    count in listOf(10, 20, 30, 50, 100, 150, 200, 300, 500, 800)
                }
                else -> {
                    // 超快：只播放大里程碑
                    count in listOf(50, 100, 200, 300, 500, 800)
                }
            }
            
            if (shouldPlay) {
                playCountFast(count, interrupt)
                Logcat.d("智能播放计数: $count, 速度: ${String.format("%.2f", speed)}/秒, 打断: $interrupt")
            }
        }
        
        /**
         * 停止当前播放
         */
        fun stop() {
            fastAudioPlayer.stop()
            Logcat.d("停止音频播放")
        }
        
        /**
         * 停止所有播放
         */
        fun stopAll() {
            fastAudioPlayer.stopAll()
            Logcat.d("停止所有音频播放")
        }
        
        /**
         * 设置播放状态回调
         */
        fun setOnPlaybackStateChanged(callback: (FastAudioPlayer.FastAudioState) -> Unit) {
            fastAudioPlayer.setOnPlaybackStateChanged(callback)
        }
        
        /**
         * 设置错误回调
         */
        fun setOnPlaybackError(callback: (String, Exception?) -> Unit) {
            fastAudioPlayer.setOnPlaybackError(callback)
        }
        
        /**
         * 检查音频是否已加载
         */
        fun isAudioLoaded(audio: PoseAudio): Boolean {
            return fastAudioPlayer.isAudioLoaded(getFullPath(audio))
        }
        
        /**
         * 获取已加载的音频数量
         */
        fun getLoadedAudioCount(): Int {
            return fastAudioPlayer.getLoadedAudioCount()
        }
        
        /**
         * 释放资源
         */
        fun release() {
            fastAudioPlayer.release()
            Logcat.d("FastPoseAudioManager 资源已释放")
        }
        
        /**
         * 重置播放状态
         */
        fun reset() {
            lastPlayedAudio = ""
            lastPlayTime = 0L
            Logcat.d("FastPoseAudioManager 状态已重置")
        }
        
        /**
         * 获取完整音频路径
         */
        fun getFullPath(audio: PoseAudio): String {
            return "${AUDIO_BASE_PATH}${audio.path}${AUDIO_EXTENSION}"
        }
    }
    
    /**
     * 姿态检测相关音频枚举
     */
    enum class PoseAudio(val path: String) {
        // 系统提示音
        WELCOME("放好手机退后两步直到能在屏幕里看到全身"),
        READY("开始跳绳吧正在给你计数喔"),
        START("倒计时321开始"),
        JUMP("每次跳绳音效"),
        BEYOND_SCREEN("请保持全身在屏幕内"),
        
        // 计数提示
        COUNT_10("跳绳播报10"),
        COUNT_20("跳绳播报20"),
        COUNT_30("跳绳播报30"),
        COUNT_40("跳绳播报40"),
        COUNT_50("跳绳播报50"),
        COUNT_60("跳绳播报60"),
        COUNT_70("跳绳播报70"),
        COUNT_80("跳绳播报80"),
        COUNT_90("跳绳播报90"),
        COUNT_100("跳绳播报100"),
        COUNT_110("跳绳播报110"),
        COUNT_120("跳绳播报120"),
        COUNT_150("跳绳播报150"),
        COUNT_200("跳绳播报200"),
        COUNT_250("跳绳播报250"),
        COUNT_300("跳绳播报300"),
        COUNT_400("跳绳播报400"),
        COUNT_500("跳绳播报500"),
        COUNT_600("跳绳播报600"),
        COUNT_700("跳绳播报700"),
        COUNT_800("跳绳播报800"),
    }
}
