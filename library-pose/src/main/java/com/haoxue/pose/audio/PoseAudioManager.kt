package com.haoxue.pose.audio

import android.content.Context
import com.haoxue.libcommon.audio.AudioQueuePlayer
import com.haoxue.libcommon.utils.AudioPlaybackUtils
import com.lazy.library.logging.Logcat

/**
 * 姿态检测音频管理器
 * 提供枚举方式访问和播放assets/voice目录下的音频文件
 */
class PoseAudioManager {
    companion object {
        private const val AUDIO_BASE_PATH = "voice/"
        private const val AUDIO_EXTENSION = ".mp3"
        private lateinit var audioPlaybackUtils: AudioPlaybackUtils

        private var lastPath = ""

        /**
         * 初始化音频播放器
         * @param context 上下文
         */
        fun initialize(context: Context) {
            audioPlaybackUtils = AudioPlaybackUtils().apply {
                initialize(context, true)
            }
            Logcat.d("PoseAudioManager 初始化完成")
        }

        /**
         * 设置播放结束后的回调地址
         */
        fun setOnPlayFinishListener(listener: (path: String) -> Unit) {
            audioPlaybackUtils.setOnPlayFinishListener(listener)
        }

        /**
         * 播放指定的音频文件
         * @param audio 音频枚举
         */
        fun play(audio: PoseAudio) {
            if (audioPlaybackUtils.getCurrentState() != AudioQueuePlayer.AudioPlaybackState.IDLE && audio.path == lastPath) {
                return
            }
            lastPath = audio.path
            if (audio.path == PoseAudio.READY.path) {
                audioPlaybackUtils.clearQueue()
            }
            if (audio.path == PoseAudio.BEYOND_SCREEN.path) {
                audioPlaybackUtils.clearQueue()
            }
            audioPlaybackUtils.playAudio(getFullPath(audio))
            Logcat.d("播放姿态音频: ${audio.path}")
        }

        fun playCount(num: Int) {
            var path = PoseAudio.JUMP
            when (num) {
                10 -> {
                    path = PoseAudio.COUNT_10
                }
                20 -> {
                    path = PoseAudio.COUNT_20
                }
                30 -> {
                    path = PoseAudio.COUNT_30
                }
                40 -> {
                    path = PoseAudio.COUNT_40
                }
                50 -> {
                    path = PoseAudio.COUNT_50
                }
                60 -> {
                    path = PoseAudio.COUNT_60
                }
                70 -> {
                    path = PoseAudio.COUNT_70
                }
                80 -> {
                    path = PoseAudio.COUNT_80
                }
                90 -> {
                    path = PoseAudio.COUNT_90
                }
                100 -> {
                    path = PoseAudio.COUNT_100
                }
                110 -> {
                    path = PoseAudio.COUNT_110
                }
                120 -> {
                    path = PoseAudio.COUNT_120
                }
                150 -> {
                    path = PoseAudio.COUNT_150
                }
                200 -> {
                    path = PoseAudio.COUNT_200
                }
                250 -> {
                    path = PoseAudio.COUNT_250
                }
                300 -> {
                    path = PoseAudio.COUNT_300
                }
                400 -> {
                    path = PoseAudio.COUNT_400
                }
                500 -> {
                    path = PoseAudio.COUNT_500
                }
                600 -> {
                    path = PoseAudio.COUNT_600
                }
                700 -> {
                    path = PoseAudio.COUNT_700
                }
                800 -> {
                    path = PoseAudio.COUNT_800
                }
            }
            play(path)
        }

        /**
         * 停止所有音频播放并清空队列
         */
        fun stopAll() {
            audioPlaybackUtils.stop()
            Logcat.d("停止所有姿态音频播放")
        }


        /**
         * 停止所有音频播放并清空队列
         */
        fun pause() {
            audioPlaybackUtils.pause()
            Logcat.d("停止所有姿态音频播放")
        }

        /**
         * 停止所有音频播放并清空队列
         */
        fun resume() {
            audioPlaybackUtils.resume()
            Logcat.d("停止所有姿态音频播放")
        }


        /**
         * 停止所有音频播放并清空队列
         */
        fun release() {
            audioPlaybackUtils.release()
            Logcat.d("停止所有姿态音频播放")
        }

        fun getFullPath(audio: PoseAudio): String {
            return "${AUDIO_BASE_PATH}${audio.path}${AUDIO_EXTENSION}"
        }

    }


    /**
     * 姿态检测相关音频枚举
     * 对应assets/voice目录下的音频文件（不含扩展名）
     */
    enum class PoseAudio(val path: String) {
        // 系统提示音
        WELCOME("放好手机退后两步直到能在屏幕里看到全身"),
        READY("开始跳绳吧正在给你计数喔"),
        START("倒计时321开始"),

        JUMP("每次跳绳音效"),

        BEYOND_SCREEN("请保持全身在屏幕内"),

        // 计数提示
        COUNT_10("跳绳播报10"),
        COUNT_20("跳绳播报20"),
        COUNT_30("跳绳播报30"),
        COUNT_40("跳绳播报40"),
        COUNT_50("跳绳播报50"),
        COUNT_60("跳绳播报60"),
        COUNT_70("跳绳播报70"),
        COUNT_80("跳绳播报80"),
        COUNT_90("跳绳播报90"),
        COUNT_100("跳绳播报100"),
        COUNT_110("跳绳播报110"),
        COUNT_120("跳绳播报120"),
        COUNT_150("跳绳播报150"),
        COUNT_200("跳绳播报200"),
        COUNT_250("跳绳播报250"),
        COUNT_300("跳绳播报300"),
        COUNT_400("跳绳播报400"),
        COUNT_500("跳绳播报500"),
        COUNT_600("跳绳播报600"),
        COUNT_700("跳绳播报700"),
        COUNT_800("跳绳播报800"),
    }
}