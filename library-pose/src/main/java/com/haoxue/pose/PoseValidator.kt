package com.haoxue.pose

import com.lazy.library.logging.Logcat

/**
 * 姿态验证工具类，用于检查人体是否完全在屏幕中
 */
class PoseValidator(
    private val confidenceThreshold: Float = 0.2f,
    private val marginPercentage: Float = 0.05f // 边缘安全距离百分比
) {
    
    /**
     * 检查人体是否完全在屏幕中
     * @param keypoints 关键点数组
     * @param imageWidth 图像宽度
     * @param imageHeight 图像高度
     * @return 包含验证结果和提示信息的对象
     */
    fun validateFullBodyInFrame(
        keypoints: FloatArray,
        imageWidth: Int,
        imageHeight: Int
    ): Boolean {
        if (keypoints.size < 17 * 3) {
            Logcat.d("检测点不满足17")
            return false
        }
        
        // 计算边缘安全区域
        val marginX = imageWidth * marginPercentage
        val marginY = imageHeight * marginPercentage
        val minX = marginX
        val maxX = imageWidth - marginX
        val minY = marginY
        val maxY = imageHeight - marginY
        
        // 需要检查的关键点索引（头部、手部、脚部）
        val criticalPoints = listOf(0, 9, 10, 15, 16) // 鼻子、左右手腕、左右脚踝
        
        // 检查关键点是否在画面中且置信度足够
        for (index in criticalPoints) {
            val x = keypoints[index * 3]
            val y = keypoints[index * 3 + 1]
            val confidence = keypoints[index * 3 + 2]
            
            // 置信度不足
            if (confidence < confidenceThreshold) {
                Logcat.d("检测点置信度不足")
                return false
            }
            
            // 超出边界
            if (x < minX || x > maxX || y < minY || y > maxY) {
                Logcat.d("检测点超出边界")
                return false
            }
        }
        
        return true
    }
}