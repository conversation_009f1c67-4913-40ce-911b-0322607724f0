package com.tencent.yolo11ncnn

/**
 * Very small-scale Hungarian (<PERSON><PERSON><PERSON>) assignment solver for float cost matrices.
 * The implementation targets matrix sizes ≤ 8 and is optimised for readability
 * rather than absolute performance. Time complexity O(n³).
 *
 * Usage: `HungarianSolver.solve(cost)` returns IntArray of size n, where
 * result[row] = assigned column index (or -1 if the cost was set to INF).
 */
object HungarianSolver {

    private const val INF = 1e9f

    fun solve(input: Array<FloatArray>): IntArray {
        val n = input.size
        // Deep copy as we will mutate matrix
        val cost = Array(n) { r -> input[r].copyOf() }

        // STEP 1: row reduction
        for (r in 0 until n) {
            var minVal = cost[r][0]
            for (c in 1 until n) if (cost[r][c] < minVal) minVal = cost[r][c]
            for (c in 0 until n) cost[r][c] -= minVal
        }
        // STEP 2: column reduction
        for (c in 0 until n) {
            var minVal = cost[0][c]
            for (r in 1 until n) if (cost[r][c] < minVal) minVal = cost[r][c]
            for (r in 0 until n) cost[r][c] -= minVal
        }

        val mask = Array(n) { IntArray(n) }  // 0: none, 1: star, 2: prime
        val rowCover = BooleanArray(n)
        val colCover = BooleanArray(n)

        // STEP 3: Star zeros
        for (r in 0 until n) {
            for (c in 0 until n) {
                if (cost[r][c] == 0f && !rowCover[r] && !colCover[c]) {
                    mask[r][c] = 1
                    rowCover[r] = true
                    colCover[c] = true
                }
            }
        }
        // Clear covers
        rowCover.fill(false)
        colCover.fill(false)

        var step = 4
        var pathRow0 = -1
        var pathCol0 = -1
        val path = Array(n * 2) { IntArray(2) }

        while (true) {
            when (step) {
                4 -> {
                    // Cover columns with starred zeros
                    for (r in 0 until n) {
                        for (c in 0 until n) {
                            if (mask[r][c] == 1) colCover[c] = true
                        }
                    }
                    val coveredCols = colCover.count { it }
                    if (coveredCols == n) {
                        // Finished
                        val result = IntArray(n) { -1 }
                        for (r in 0 until n) for (c in 0 until n) if (mask[r][c] == 1) result[r] = c
                        return result
                    }
                    step = 5
                }
                5 -> {
                    // Find zero Z in uncovered cell, prime it.
                    var done = false
                    var row = -1
                    var col = -1
                    while (!done) {
                        val zero = findUncoveredZero(cost, rowCover, colCover)
                        row = zero.first
                        col = zero.second
                        if (row == -1) {
                            step = 7
                            done = true
                        } else {
                            mask[row][col] = 2
                            // Check for starred zero in the same row
                            val starCol = mask[row].indexOfFirst { it == 1 }
                            if (starCol != -1) {
                                rowCover[row] = true
                                colCover[starCol] = false
                            } else {
                                step = 6
                                pathRow0 = row
                                pathCol0 = col
                                done = true
                            }
                        }
                    }
                }
                6 -> {
                    // Augmenting path start
                    var pathLen = 0
                    path[pathLen][0] = pathRow0
                    path[pathLen][1] = pathCol0
                    var done = false
                    while (!done) {
                        val row = findStarInCol(mask, path[pathLen][1])
                        if (row != -1) {
                            pathLen++
                            path[pathLen][0] = row
                            path[pathLen][1] = path[pathLen - 1][1]
                        } else {
                            done = true
                            break
                        }
                        val col = findPrimeInRow(mask, path[pathLen][0])
                        pathLen++
                        path[pathLen][0] = path[pathLen - 1][0]
                        path[pathLen][1] = col
                    }
                    // Toggle starred / primed along the augmenting path
                    for (i in 0..pathLen) {
                        val r = path[i][0]
                        val c = path[i][1]
                        if (mask[r][c] == 1) mask[r][c] = 0 else if (mask[r][c] == 2) mask[r][c] = 1
                    }
                    // Clear covers and primes
                    rowCover.fill(false)
                    colCover.fill(false)
                    for (r in 0 until n) for (c in 0 until n) if (mask[r][c] == 2) mask[r][c] = 0
                    step = 4
                }
                7 -> {
                    // Add smallest uncovered value to covered rows, subtract from uncovered cols
                    var minVal = INF
                    for (r in 0 until n) {
                        if (!rowCover[r]) {
                            for (c in 0 until n) if (!colCover[c] && cost[r][c] < minVal) minVal = cost[r][c]
                        }
                    }
                    for (r in 0 until n) {
                        if (rowCover[r]) for (c in 0 until n) cost[r][c] += minVal
                    }
                    for (c in 0 until n) {
                        if (!colCover[c]) for (r in 0 until n) cost[r][c] -= minVal
                    }
                    step = 5
                }
            }
        }
    }

    // ---------------------------------------------------------------------
    private fun findUncoveredZero(cost: Array<FloatArray>, rowCover: BooleanArray, colCover: BooleanArray): Pair<Int, Int> {
        val n = cost.size
        for (r in 0 until n) {
            if (!rowCover[r]) {
                for (c in 0 until n) {
                    if (!colCover[c] && cost[r][c] == 0f) return r to c
                }
            }
        }
        return -1 to -1
    }

    private fun findStarInCol(mask: Array<IntArray>, col: Int): Int {
        val n = mask.size
        for (r in 0 until n) if (mask[r][col] == 1) return r
        return -1
    }

    private fun findPrimeInRow(mask: Array<IntArray>, row: Int): Int {
        val n = mask.size
        for (c in 0 until n) if (mask[row][c] == 2) return c
        return -1
    }

    // Helper extension
    private fun IntArray.indexOfFirst(predicate: (Int) -> Boolean): Int {
        for (i in indices) if (predicate(this[i])) return i
        return -1
    }
} 