package com.haoxue.pose

import com.haoxue.pose.audio.PoseAudioManager
import com.lazy.library.logging.Logcat

/**
 * 智能音频管理器
 * 解决快速跳绳时音频播放延迟问题
 */
class SmartAudioManager {
    
    companion object {
        // 跟踪上次播放的计数和时间
        private var lastPlayedCount = 0
        private var lastPlayTime = 0L
        private var jumpSpeed = 0f // 每秒跳绳次数
        
        // 速度阈值定义
        private const val SLOW_SPEED = 1.5f     // 每秒1.5次以下为慢速
        private const val NORMAL_SPEED = 3.0f   // 每秒3次以下为正常速度
        private const val FAST_SPEED = 5.0f     // 每秒5次以下为快速
        
        /**
         * 智能播放计数音频
         * 根据跳绳速度自动调整播放策略
         */
        fun smartPlayCount(count: Int) {
            val currentTime = System.currentTimeMillis()
            
            // 计算跳绳速度
            if (count > lastPlayedCount && lastPlayTime > 0) {
                val timeDiff = currentTime - lastPlayTime
                val countDiff = count - lastPlayedCount
                jumpSpeed = if (timeDiff > 0) (countDiff * 1000f) / timeDiff else 0f
            }
            
            // 只在计数变化时处理
            if (count > lastPlayedCount) {
                handleAudioBySpeed(count)
                lastPlayedCount = count
                lastPlayTime = currentTime
            }
        }
        
        /**
         * 根据速度处理音频播放
         */
        private fun handleAudioBySpeed(count: Int) {
            when {
                jumpSpeed <= SLOW_SPEED -> {
                    // 慢速：播放所有重要计数
                    if (shouldPlayAudio(count, PlayMode.ALL)) {
                        PoseAudioManager.playCount(count)
                        Logcat.d("慢速播放计数音频: $count, 速度: ${String.format("%.2f", jumpSpeed)}/秒")
                    }
                }
                jumpSpeed <= NORMAL_SPEED -> {
                    // 正常速度：播放重要节点，清空队列避免延迟
                    if (shouldPlayAudio(count, PlayMode.IMPORTANT)) {
                        PoseAudioManager.clearQueue()
                        PoseAudioManager.playCount(count)
                        Logcat.d("正常速度播放计数音频: $count, 速度: ${String.format("%.2f", jumpSpeed)}/秒")
                    }
                }
                jumpSpeed <= FAST_SPEED -> {
                    // 快速：只播放里程碑，立即播放
                    if (shouldPlayAudio(count, PlayMode.MILESTONES)) {
                        PoseAudioManager.playCountFast(count)
                        Logcat.d("快速播放计数音频: $count, 速度: ${String.format("%.2f", jumpSpeed)}/秒")
                    }
                }
                else -> {
                    // 超快速：只播放大里程碑
                    if (shouldPlayAudio(count, PlayMode.MAJOR_MILESTONES)) {
                        PoseAudioManager.playCountFast(count)
                        Logcat.d("超快速播放计数音频: $count, 速度: ${String.format("%.2f", jumpSpeed)}/秒")
                    }
                }
            }
        }
        
        /**
         * 播放模式枚举
         */
        private enum class PlayMode {
            ALL,                // 播放所有
            IMPORTANT,          // 播放重要节点
            MILESTONES,         // 播放里程碑
            MAJOR_MILESTONES    // 播放大里程碑
        }
        
        /**
         * 判断是否应该播放音频
         */
        private fun shouldPlayAudio(count: Int, mode: PlayMode): Boolean {
            return when (mode) {
                PlayMode.ALL -> {
                    // 播放所有预定义的计数
                    count in listOf(10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 150, 200, 250, 300, 400, 500, 600, 700, 800)
                }
                PlayMode.IMPORTANT -> {
                    // 播放重要节点（每10个 + 特殊节点）
                    count % 10 == 0 || count in listOf(50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 800)
                }
                PlayMode.MILESTONES -> {
                    // 播放里程碑
                    count in listOf(10, 20, 30, 50, 100, 150, 200, 300, 500, 800)
                }
                PlayMode.MAJOR_MILESTONES -> {
                    // 只播放大里程碑
                    count in listOf(50, 100, 200, 300, 500, 800)
                }
            }
        }
        
        /**
         * 重置状态
         */
        fun reset() {
            lastPlayedCount = 0
            lastPlayTime = 0L
            jumpSpeed = 0f
            Logcat.d("SmartAudioManager 状态已重置")
        }
        
        /**
         * 获取当前跳绳速度
         */
        fun getCurrentSpeed(): Float = jumpSpeed
        
        /**
         * 获取速度描述
         */
        fun getSpeedDescription(): String {
            return when {
                jumpSpeed <= SLOW_SPEED -> "慢速"
                jumpSpeed <= NORMAL_SPEED -> "正常"
                jumpSpeed <= FAST_SPEED -> "快速"
                else -> "超快"
            }
        }
    }
}
