package com.haoxue.pose.dialog

import android.content.Context
import android.widget.ImageView
import com.haoxue.libcommon.singleClick
import com.haoxue.libcommon.ui.dialog.CommonBaseDialog
import com.haoxue.pose.R

class InitTipsDialog(mContext: Context,  val callback: () -> Unit) : CommonBaseDialog(mContext) {
    public override fun initData() {
        setCanceledOnTouchOutside(false)
        setCancelable(false)
        var content = findViewById<ImageView>(R.id.content)
        content.singleClick {
            dismiss()
            callback()
        }
    }

    public override fun setContentLayout(): Int {
        return R.layout.dialog_init_tips
    }
}
