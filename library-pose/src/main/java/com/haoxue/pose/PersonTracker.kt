package com.tencent.yolo11ncnn

import kotlin.math.hypot
import kotlin.math.max

/**
 * Multi-person tracker using constant-velocity Kalman filter + Hungarian assignment.
 * Compared with the previous greedy nearest-neighbour version, this implementation
 * 1) produces stabler IDs when detections jitter or miss, and
 * 2) offers predicted positions that can be used as smoothed key points.
 *
 * The external API保持不变：给定当前帧人物中心点 (cx,cy) 列表，返回与之顺序一致的稳定 id 数组。
 */
class PersonTracker(
    private val distThreshold: Float = 150f, // gating threshold (pixel)
    private val maxLost: Int = 60            // frames allowed to be missing before a track is dropped
) {

    private data class Track(
        val id: Int,
        val kf: KalmanFilter2D,
        var lost: Int = 0
    ) {
        fun predict(): Pair<Float, Float> = kf.predict()
        fun update(cx: Float, cy: Float) = kf.update(cx, cy)
        val x: Float get() = kf.x[0]
        val y: Float get() = kf.x[1]
    }

    private val tracks = mutableListOf<Track>()
    private var nextId = 0

    /**
     * 核心接口：输入检测到的人体中心点列表，返回稳定 id 数组，与输入顺序一致。
     */
    fun assign(centers: List<Pair<Float, Float>>): IntArray {
        val detCount = centers.size
        val results = IntArray(detCount) { -1 }

        // 1. 预测现有 Track 的位置
        val predictions = tracks.map { it.predict() }

        // 2. 构造 costMatrix (欧氏距离)
        val trackCount = tracks.size
        if (trackCount == 0) {
            // 没有历史轨迹，全部新建
            centers.forEachIndexed { idx, (cx, cy) ->
                val track = createTrack(cx, cy)
                tracks.add(track)
                results[idx] = track.id
            }
            return results
        }

        // 构造方阵，边长 = max(trackCount, detCount)
        val size = max(trackCount, detCount)
        val big = 1e6f
        val cost = Array(size) { FloatArray(size) { big } }

        for (t in 0 until trackCount) {
            val (px, py) = predictions[t]
            for (d in 0 until detCount) {
                val (cx, cy) = centers[d]
                val dist = hypot(px - cx, py - cy)
                cost[t][d] = dist
            }
        }

        // 3. Hungarian
        val assignment = HungarianSolver.solve(cost)

        val unmatchedTracks = mutableSetOf<Int>()
        for (i in 0 until trackCount) unmatchedTracks.add(i)
        val unmatchedDets = mutableSetOf<Int>()
        for (i in 0 until detCount) unmatchedDets.add(i)

        // 4. 处理匹配结果
        for (t in 0 until trackCount) {
            val d = assignment[t]
            if (d >= 0 && d < detCount && cost[t][d] < distThreshold) {
                // 匹配成功
                tracks[t].update(centers[d].first, centers[d].second)
                tracks[t].lost = 0
                results[d] = tracks[t].id
                unmatchedTracks.remove(t)
                unmatchedDets.remove(d)
            }
        }

        // 5. 未匹配 Track 进行老化
        unmatchedTracks.forEach { idx ->
            val tr = tracks[idx]
            tr.lost++
        }

        // 6. 删除过久未匹配 Track
        tracks.removeAll { it.lost > maxLost }

        // 7. 为未匹配检测创建新 Track
        unmatchedDets.forEach { detIdx ->
            val (cx, cy) = centers[detIdx]
            val track = createTrack(cx, cy)
            tracks.add(track)
            results[detIdx] = track.id
        }

        return results
    }

    fun reset() {
        tracks.clear()
        nextId = 0
    }

    // --------------------------------------------------------------------

    private fun createTrack(cx: Float, cy: Float): Track {
        val kf = KalmanFilter2D()
        kf.init(cx, cy)
        return Track(nextId++, kf)
    }
} 