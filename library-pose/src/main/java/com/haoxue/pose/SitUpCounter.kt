package com.tencent.yolo11ncnn

import com.tencent.yolo11ncnn.MotionCounter

/**
 * TODO: 仰卧起坐计数逻辑待实现。
 */
class SitUpCounter : MotionCounter {

    private val countMap = mutableMapOf<Int, Int>()
    private var maxPerson = 1

    override fun processPerson(
        id: Int,
        imageWidth: Int,
        imageHeight: Int,
        keypoints: FloatArray
    ): Int? {
        if (countMap.size >= maxPerson && !countMap.containsKey(id)) return null
        countMap[id] = 0
        return 0
    }

    override fun reset() { countMap.clear() }

    override fun setMaxPerson(maxPerson: Int) { this.maxPerson = maxPerson }
} 