package com.tencent.yolo11ncnn

/**
 * Extremely lightweight 2-D constant-velocity Kalman filter.
 * Designed for mobile real-time multi-object tracking where the state is just
 * position (x,y) and velocity (vx,vy). All matrices are hard-coded 4×4 / 2×4
 * to avoid dynamic allocation and keep GC pressure minimal.
 */
class KalmanFilter2D(
    // Time interval between two successive frames (in arbitrary unit, here frame = 1)
    private val dt: Float = 1f,
    // Process noise variance for acceleration. Larger => quicker to adapt, noisier output.
    private val accelVar: Float = 1f,
    // Measurement noise variance (pixel^2). Larger => more trust on prediction.
    private val measVar: Float = 100f
) {

    /** State vector [x, y, vx, vy]ᵀ */
    val x = FloatArray(4)

    /** Covariance matrix P (4×4) */
    private val P = Array(4) { FloatArray(4) }

    // Constant matrices ----------------------------------------------------

    /** State transition matrix F (4×4) */
    private val F = arrayOf(
        floatArrayOf(1f, 0f, dt, 0f),
        floatArrayOf(0f, 1f, 0f, dt),
        floatArrayOf(0f, 0f, 1f, 0f),
        floatArrayOf(0f, 0f, 0f, 1f)
    )

    /** Observation matrix H (2×4) */
    private val H = arrayOf(
        floatArrayOf(1f, 0f, 0f, 0f),
        floatArrayOf(0f, 1f, 0f, 0f)
    )

    /** Process noise Q (4×4) */
    private val Q = Array(4) { FloatArray(4) }

    /** Measurement noise R (2×2) */
    private val R = arrayOf(
        floatArrayOf(measVar, 0f),
        floatArrayOf(0f, measVar)
    )

    init {
        // Initialise P to identity * large value to reflect high initial uncertainty
        for (i in 0 until 4) P[i][i] = 1000f

        // Build Q based on constant-acceleration model simplified for CV.
        // Q = [[dt^4/4, 0, dt^3/2, 0], ...]
        val dt2 = dt * dt
        val dt3_2 = dt2 * dt / 2f
        val dt4_4 = dt2 * dt2 / 4f

        Q[0][0] = dt4_4 * accelVar
        Q[1][1] = dt4_4 * accelVar
        Q[0][2] = dt3_2 * accelVar
        Q[1][3] = dt3_2 * accelVar
        Q[2][0] = dt3_2 * accelVar
        Q[3][1] = dt3_2 * accelVar
        Q[2][2] = dt2 * accelVar
        Q[3][3] = dt2 * accelVar
    }

    /** Set initial position (velocity set to 0). */
    fun init(posX: Float, posY: Float) {
        x[0] = posX
        x[1] = posY
        x[2] = 0f
        x[3] = 0f
    }

    /** Predict state one step ahead and return predicted (x,y). */
    fun predict(): Pair<Float, Float> {
        // x = F * x
        val px = x[0] + dt * x[2]
        val py = x[1] + dt * x[3]
        x[0] = px
        x[1] = py
        // velocities remain the same

        // P = F P Fᵀ + Q (compute in place)
        val FP = Array(4) { FloatArray(4) }
        multiply(F, P, FP)          // FP = F*P
        val FPFt = Array(4) { FloatArray(4) }
        multiply(FP, F, FPFt, transposeB = true) // FPFᵀ
        addInPlace(FPFt, Q, P)      // P = FPFᵀ + Q (store in P)
        return px to py
    }

    /** Update with measured (x,y). */
    fun update(measX: Float, measY: Float) {
        // y = z - Hx (innovation 2×1)
        val y0 = measX - x[0]
        val y1 = measY - x[1]

        // S = H P Hᵀ + R  (2×2)
        val HP = Array(2) { FloatArray(4) }
        multiply(H, P, HP)
        val S = Array(2) { FloatArray(2) }
        multiply(HP, H, S, transposeB = true)
        S[0][0] += R[0][0]
        S[1][1] += R[1][1]

        // K = P Hᵀ S⁻¹  (4×2)
        val PHt = Array(4) { FloatArray(2) }
        multiply(P, H, PHt, transposeB = true)
        val SInv = invert2x2(S)
        val K = Array(4) { FloatArray(2) }
        multiply(PHt, SInv, K)

        // x = x + K y
        for (i in 0 until 4) {
            x[i] += K[i][0] * y0 + K[i][1] * y1
        }

        // P = (I - K H) P
        val KH = Array(4) { FloatArray(4) }
        multiply(K, H, KH)
        val IminusKH = Array(4) { FloatArray(4) }
        for (i in 0 until 4) {
            IminusKH[i][i] = 1f - KH[i][i]
            for (j in 0 until 4) {
                if (i != j) IminusKH[i][j] = -KH[i][j]
            }
        }
        val newP = Array(4) { FloatArray(4) }
        multiply(IminusKH, P, newP)
        // copy back to P
        for (i in 0 until 4) {
            for (j in 0 until 4) P[i][j] = newP[i][j]
        }
    }

    // -----------------------------------------------------------
    // Basic small-matrix helper functions (no dynamic alloc where possible)

    private fun multiply(
        A: Array<FloatArray>,
        B: Array<FloatArray>,
        out: Array<FloatArray>,
        transposeA: Boolean = false,
        transposeB: Boolean = false
    ) {
        val aRows = if (transposeA) A[0].size else A.size
        val aCols = if (transposeA) A.size else A[0].size
        val bCols = if (transposeB) B.size else B[0].size
        for (i in 0 until aRows) {
            for (j in 0 until bCols) {
                var sum = 0f
                for (k in 0 until aCols) {
                    val a = if (transposeA) A[k][i] else A[i][k]
                    val b = if (transposeB) B[j][k] else B[k][j]
                    sum += a * b
                }
                out[i][j] = sum
            }
        }
    }

    private fun addInPlace(A: Array<FloatArray>, B: Array<FloatArray>, out: Array<FloatArray>) {
        for (i in A.indices) {
            for (j in A[0].indices) {
                out[i][j] = A[i][j] + B[i][j]
            }
        }
    }

    private fun invert2x2(M: Array<FloatArray>): Array<FloatArray> {
        val det = M[0][0] * M[1][1] - M[0][1] * M[1][0]
        if (det == 0f) return arrayOf(floatArrayOf(0f, 0f), floatArrayOf(0f, 0f))
        val invDet = 1f / det
        return arrayOf(
            floatArrayOf(M[1][1] * invDet, -M[0][1] * invDet),
            floatArrayOf(-M[1][0] * invDet, M[0][0] * invDet)
        )
    }
} 